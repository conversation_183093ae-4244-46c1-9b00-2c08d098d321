<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import piniaStores from '../stores/apiStore'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { getApiUrl } from '../config/apiConfig'

import CartItem from '../components/CartItem.vue'
import _ from 'lodash'

const router = useRouter()

const handelPinia = piniaStores()
const { clientData, carts, total, isUser } = storeToRefs(handelPinia)
// 不需要 props，因為我們直接使用 Pinia store 中的數據

const isSendOrder = ref(false)

// 表單資料 - 移除 address 欄位
const reactiveData = reactive({
  receiver: '',
  phone: '',
  detailed_address: '', // 新增住址
  cooperative: '',      // 新增儲互社
  invoice_title: '',    // 新增抬頭
  tax_id: '',           // 新增統編
  notes: '',            // 新增備註
})

// 表單錯誤訊息 - 移除 address 欄位
const formErrors = reactive({
  receiver: '',
  phone: '',
  detailed_address: '',
  cooperative: '',
  invoice_title: '',
  tax_id: '',
  notes: '',
})

// 表單驗證狀態 - 移除 address 欄位
const formTouched = reactive({
  receiver: false,
  phone: false,
  detailed_address: false,
  cooperative: false,
  invoice_title: false,
  tax_id: false,
  notes: false,
})

// 通用錯誤訊息
const errorMessage = ref('')

// 驗證電話號碼格式
const validatePhone = (phone) => {
  // 只允許數字
  const numericRegex = /^[0-9]+$/
  if (!numericRegex.test(phone)) {
    return '電話號碼只能包含數字'
  }
  
  // 台灣手機號碼格式驗證 (09開頭，共10碼)
  const mobileRegex = /^09\d{8}$/
  // 市話格式驗證 (2-3碼區碼 + 6-8碼電話號碼)
  const telRegex = /^0\d{1,2}\d{6,8}$/
  
  if (!mobileRegex.test(phone) && !telRegex.test(phone)) {
    return '請輸入有效的台灣電話號碼格式'
  }
  
  return ''
}

// 實時驗證表單 - 移除地址驗證
const validateForm = () => {
  // 驗證訂購人
  if (!reactiveData.receiver.trim()) {
    formErrors.receiver = '請輸入訂購人姓名'
  } else {
    formErrors.receiver = ''
  }
  
  // 驗證電話
  if (!reactiveData.phone.trim()) {
    formErrors.phone = '請輸入聯絡電話'
  } else {
    formErrors.phone = validatePhone(reactiveData.phone)
  }
  // 住址可選填
  // 其餘欄位可選填，不做強制驗證
}

// 處理輸入欄位變更
const handleInputChange = (field) => {
  formTouched[field] = true
  validateForm()
}

// 處理電話號碼輸入，只允許數字
const handlePhoneInput = (event) => {
  // 移除非數字字符
  const value = event.target.value
  reactiveData.phone = value.replace(/\D/g, '')
  formTouched.phone = true
  validateForm()
}

// 表單是否有效 - 移除地址驗證
const isFormValid = computed(() => {
  return (
    reactiveData.receiver.trim() !== '' &&
    reactiveData.phone.trim() !== '' &&
    formErrors.receiver === '' &&
    formErrors.phone === ''
  )
})

// 監控購物車狀態變化
watch(carts, (newCarts) => {
  // 如果購物車被清空且不是訂單成功狀態，導向首頁
  if ((!newCarts || newCarts.length === 0) && !isSendOrder.value) {
    console.log('購物車已清空，導向首頁')
    // 添加延遲以避免在訂單提交過程中誤觸發
    setTimeout(() => {
      if (!isSendOrder.value && (!carts.value || carts.value.length === 0)) {
        router.push('/')
      }
    }, 100)
  }
}, { deep: true })

onMounted(() => {
  // 頁面加載時檢查用戶登入狀態
  const token = localStorage.getItem('token')
  const isLogin = localStorage.getItem('isLogin')

  // 登入狀態檢查邏輯調整，檢查 localStorage 中的狀態而不只依賴 isUser
  if (!isLogin || !token) {
    // 保存當前頁面URL，以便登入後返回
    localStorage.setItem('redirect_after_login', window.location.href)
    
    router.push('/login')
    return
  }

  // 同步設置用戶狀態
  if (isLogin && token && !isUser.value) {
    isUser.value = true
    console.log('已同步設置用戶登入狀態')
  }

  // 如果購物車為空且不是訂單成功狀態，導向回首頁
  if ((!carts.value || carts.value.length === 0) && !isSendOrder.value) {
    console.log('購物車為空，導向首頁')
    router.push('/')
    return
  }
  
  // 如果用戶資料存在，預先填入表單 - 移除地址預填
  if (clientData.value) {
    reactiveData.receiver = clientData.value.name || ''
    reactiveData.phone = clientData.value.phone || ''
    
    // 驗證預填資料
    if (reactiveData.receiver) formTouched.receiver = true
    if (reactiveData.phone) formTouched.phone = true
    validateForm()
  }
})

const sendOrder = _.throttle(
  async () => {
    // 清除錯誤訊息
    errorMessage.value = ''
    
    // 標記所有欄位為已觸碰，觸發完整驗證 - 移除地址驗證
    formTouched.receiver = true
    formTouched.phone = true
    validateForm()
    
    // 檢查表單是否有效
    if (!isFormValid.value) {
      errorMessage.value = '請填寫所有必填欄位並修正錯誤'
      return
    }

    // 檢查購物車是否有商品
    if (!carts.value || carts.value.length === 0) {
      errorMessage.value = '購物車是空的，請先添加商品'
      router.push('/')
      return
    }

    // 檢查總金額是否已計算
    if (!total.value || total.value <= 0) {
      errorMessage.value = '訂單總金額有誤，請重新整理頁面'
      return
    }

    try {
      // 使用用戶實際資料 - 移除地址欄位
      const orderData = {
        receiver: reactiveData.receiver,
        phone: reactiveData.phone,
        address: '自取/無須配送',
        detailed_address: reactiveData.detailed_address,
        cooperative: reactiveData.cooperative,
        invoice_title: reactiveData.invoice_title,
        tax_id: reactiveData.tax_id,
        notes: reactiveData.notes,
        products: [...carts.value],
        total: total.value,
        client: clientData.value?.id || '1', // 使用實際用戶ID
        status: 1,
      }

      console.log('送出訂單資料:', orderData)

      // 添加認證標頭以確保正確的身份驗證
      const res = await axios.post(getApiUrl('orderList'), orderData, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      })

      console.log('訂單提交結果:', res.data)

      if (res.data && (res.data.success || res.status == 200 || res.status == 201)) {
        // 設置訂單提交成功狀態
        isSendOrder.value = true
        
        // 清空購物車
        carts.value = []
        total.value = 0
        localStorage.setItem('carts', JSON.stringify([]))
        
        // 顯示成功提示
        message.success('訂單提交成功！')
        
        // 三秒後自動導向到訂單頁面
        setTimeout(() => {
          if (isSendOrder.value) {
            router.push('/account')
          }
        }, 3000)
      } else {
        errorMessage.value = res.data?.message || '訂單提交失敗，請稍後再試'
      }
    } catch (error) {
      console.error('訂單提交錯誤:', error)
      // 提供更詳細的錯誤訊息
      if (error.response) {
        // 伺服器回應了錯誤狀態碼
        errorMessage.value = `訂單提交失敗：${error.response.status} - ${error.response.data?.message || '伺服器錯誤'}`
      } else if (error.request) {
        // 請求已發送但未收到回應
        errorMessage.value = '無法連接到伺服器，請檢查網路連接並稍後再試'
      } else {
        // 請求設置過程中出錯
        errorMessage.value = `訂單提交過程中發生錯誤：${error.message}`
      }
    }
  },
  2000,
  { trailing: false },
)
</script>

<template>
  <div class="container m-auto px-6 xl:px-0 py-10" v-if="carts.length > 0">
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-10">
      <div class="bg-gray-200 p-8 rounded-lg md:col-span-2">
        <h3 class="text-3xl font-bold mb-6">購物明細</h3>
        <CartItem></CartItem>

        <!-- 顯示總金額 -->
        <div class="flex justify-end mt-4 border-t-2 border-gray-400 pt-4">
          <p class="text-2xl font-bold">訂單總金額: NT$ {{ total.toLocaleString() }}</p>
        </div>
      </div>

      <div class="xl:mt-20">
        <h4 class="text-3xl font-bold">訂購資料</h4>
        <p class="text-gray-600 mb-4"></p>
        <form @submit.prevent="sendOrder" class="grid grid-cols-2 gap-4 mt-4">
          <div class="mb-4">
            <label for="receiver" class="block mb-1">
              訂購人 <span class="text-red-500">*</span>
            </label>
            <input
              id="receiver"
              class="w-full border-2 p-2 rounded"
              :class="{ 'border-red-500': formTouched.receiver && formErrors.receiver }"
              placeholder="請輸入姓名"
              v-model="reactiveData.receiver"
              @input="handleInputChange('receiver')"
              @blur="formTouched.receiver = true; validateForm()"
              required
            />
            <p v-if="formTouched.receiver && formErrors.receiver" class="text-red-500 text-sm mt-1">
              {{ formErrors.receiver }}
            </p>
          </div>
          
          <div class="mb-4">
            <label for="phone" class="block mb-1">
              聯絡電話 <span class="text-red-500">*</span>
            </label>
            <input
              id="phone"
              class="w-full border-2 p-2 rounded"
              :class="{ 'border-red-500': formTouched.phone && formErrors.phone }"
              placeholder="請輸入連絡電話 (僅數字)"
              v-model="reactiveData.phone"
              @input="handlePhoneInput"
              @blur="formTouched.phone = true; validateForm()"
              required
            />
            <p v-if="formTouched.phone && formErrors.phone" class="text-red-500 text-sm mt-1">
              {{ formErrors.phone }}
            </p>
            <p v-else class="text-gray-500 text-sm mt-1">
              請輸入有效的台灣手機或市話號碼
            </p>
          </div>
          
          <!-- 住址（詳細地址） -->
          <div class="mb-4">
            <label for="detailed_address" class="block mb-1">
              住址（詳細地址）
            </label>
            <input
              id="detailed_address"
              class="w-full border-2 p-2 rounded"
              placeholder="可選填"
              v-model="reactiveData.detailed_address"
              @input="handleInputChange('detailed_address')"
            />
          </div>
          
          <!-- 儲互社 -->
          <div class="mb-4">
            <label for="cooperative" class="block mb-1">
              儲互社（XX區XX社）
            </label>
            <input
              id="cooperative"
              class="w-full border-2 p-2 rounded"
              placeholder="可選填"
              v-model="reactiveData.cooperative"
              @input="handleInputChange('cooperative')"
            />
          </div>
          
          <!-- 抬頭 -->
          <div class="mb-4">
            <label for="invoice_title" class="block mb-1">
              公司抬頭
            </label>
            <input
              id="invoice_title"
              class="w-full border-2 p-2 rounded"
              placeholder="可選填"
              v-model="reactiveData.invoice_title"
              @input="handleInputChange('invoice_title')"
            />
          </div>
          
          <!-- 統編 -->
          <div class="mb-4">
            <label for="tax_id" class="block mb-1">
              統編
            </label>
            <input
              id="tax_id"
              class="w-full border-2 p-2 rounded"
              placeholder="可選填"
              v-model="reactiveData.tax_id"
              @input="handleInputChange('tax_id')"
            />
          </div>
          
          <!-- 備註 -->
          <div class="mb-4 col-span-2">
            <label for="notes" class="block mb-1">
              備註
            </label>
            <textarea
              id="notes"
              class="w-full border-2 p-2 rounded"
              placeholder="可選填"
              rows="3"
              v-model="reactiveData.notes"
              @input="handleInputChange('notes')"
            ></textarea>
          </div>

          <div class="col-span-2">
            <p v-if="errorMessage" class="text-red-500 text-center mb-2">{{ errorMessage }}</p>
            <button
              type="submit"
              class="bg-black text-white w-full p-3 rounded-lg hover:bg-gray-800 transition mt-4"
              :class="{ 'opacity-50 cursor-not-allowed': !isFormValid }"
              :disabled="!isFormValid"
            >
              送出訂單
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <div
    class="container m-auto py-10 text-center flex flex-col items-center"
    style="min-height: 50vh"
    v-if="isSendOrder"
  >
    <img src="../assets/images/icon/task.svg" class="w-20" alt="menu" />
    <p class="text-2xl mt-4 mb-6">成功送出訂單</p>
    <p class="text-gray-600 mb-4">您的訂單已成功提交，請到店自取商品</p>
    <div class="mt-10">
      <RouterLink to="/" class="mr-4 bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-black">
        返回首頁
      </RouterLink>
      <RouterLink
        to="/account"
        class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
      >
        查看訂單
      </RouterLink>
    </div>
  </div>
  <!-- 只在訂單成功送出時顯示成功頁面 -->
  <div class="container m-auto text-center py-20" v-else-if="isSendOrder">
    <div class="flex flex-col justify-center items-center">
      <img src="@/assets/images/icon/task.svg" class="w-20" alt="menu" />
      <p class="text-2xl mt-4 mb-6">成功送出訂單</p>
      <p class="text-gray-600 mb-4">您的訂單已成功提交，請到店自取商品</p>
      <div class="mt-10">
        <RouterLink to="/" class="mr-4 bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-black">
          返回首頁
        </RouterLink>
        <RouterLink
          to="/account"
          class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
        >
          查看訂單
        </RouterLink>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 為必填欄位添加樣式 */
label span.text-red-500 {
  font-weight: bold;
}

/* 輸入框聚焦時的樣式 */
input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 1px #4f46e5;
}

/* 錯誤輸入框聚焦時的樣式 */
input.border-red-500:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 1px #ef4444;
}
</style>
