import{_ as L,q as W,s as j,r as n,o as S,Q as q,d as z,e as s,f as o,g as a,h,w as D,z as N,j as u,u as p,Y as I,t as d,p as x,m as E}from"./index-da48dc81.js";const V={key:0,class:"aspect-square bg-gray-100 animate-pulse"},B={key:1,class:"relative aspect-square"},O=["src","alt"],Q=["src","alt"],U={key:2,class:"absolute inset-0 flex items-center justify-center bg-gray-50"},A={key:3,class:"absolute inset-0 flex flex-col items-center justify-center bg-gray-100 text-gray-500 text-sm"},F={key:4,class:"absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400"},M={class:"text-center product-info mt-4"},R={class:"product-name-container"},Y={class:"product-name text-sm md:text-base"},G={class:"product-price"},H={key:0,class:"flex flex-col items-center space-y-0 leading-tight"},J={class:"text-gray-500 line-through text-xs md:text-sm mb-0 pb-0"},K={class:"text-red-500 font-bold mt-0 pt-0 text-xs md:text-sm"},X={class:"text-lg md:text-xl text-pink-600"},Z={key:1,class:"text-sm md:text-base"},$={key:2,class:"text-sm md:text-base"},_={__name:"ProductItem",props:{productData:{type:Object,required:!0},keyWord:{type:String}},setup(t){const b=W(),{isUser:k}=j(b),r=t,y=n(null),c=n(!1),i=n(!1),m=n(!1),l=n(null);n(!1);const g={style:"currency",currency:"TWD",minimumFractionDigits:0,maximumFractionDigits:0};S(()=>{if(!window.IntersectionObserver){m.value=!0;return}l.value=new IntersectionObserver(v=>{v.forEach(e=>{var f;e.isIntersecting&&(m.value=!0,(f=l.value)==null||f.unobserve(e.target))})},{rootMargin:"50px",threshold:.1}),y.value&&l.value.observe(y.value)}),q(()=>{l.value&&l.value.disconnect()});const w=v=>E(v),P=()=>{c.value=!0,i.value=!1},T=()=>{i.value=!0,c.value=!1},C=()=>{b.addCarts({id:r.productData.id,name:r.productData.name,price:r.productData.price2,img:r.productData.images[0],format:r.productData.format2,count:1,minPurchaseQty:r.productData.minPurchaseQty||1,minPurchaseAmount:r.productData.minPurchaseAmount||null})};return(v,e)=>{const f=z("a-tag");return s(),o("div",null,[a("div",{ref_key:"imageContainer",ref:y,class:"relative overflow-hidden group"},[h(p(I),{to:{path:"/product",query:{id:t.productData.id,sort:t.keyWord}},class:"font-bold"},{default:D(()=>[m.value?(s(),o("div",B,[t.productData.images&&t.productData.images.length>0?(s(),o("img",{key:0,src:w(t.productData.images[0]),alt:t.productData.name,class:N(["w-full h-full object-cover transition-opacity duration-300",{"opacity-0":!c.value&&!i.value,"opacity-100":c.value}]),onLoad:P,onError:T},null,42,O)):u("",!0),t.productData.images&&t.productData.images.length>1&&c.value?(s(),o("img",{key:1,src:w(t.productData.images[1]),alt:t.productData.name,class:"absolute inset-0 w-full h-full object-cover opacity-0 group-hover:opacity-100 transition-opacity duration-300"},null,8,Q)):u("",!0),!c.value&&!i.value&&m.value?(s(),o("div",U,e[0]||(e[0]=[a("div",{class:"w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"},null,-1)]))):u("",!0),i.value?(s(),o("div",A,e[1]||(e[1]=[a("i",{class:"fas fa-image text-2xl mb-2"},null,-1),a("span",null,"圖片載入失敗",-1)]))):u("",!0),!t.productData.images||t.productData.images.length===0?(s(),o("div",F,e[2]||(e[2]=[a("i",{class:"fas fa-image text-3xl"},null,-1)]))):u("",!0)])):(s(),o("div",V))]),_:1},8,["to"])],512),a("div",M,[a("div",R,[h(p(I),{to:{path:"/product",query:{id:t.productData.id}},class:"hover:no-underline text-black hover:text-black"},{default:D(()=>[a("h3",Y,d(t.productData.name),1)]),_:1},8,["to"])]),a("div",G,[p(k)&&t.productData.price2&&t.productData.price1&&t.productData.price2<t.productData.price1?(s(),o("div",H,[a("p",J," 建議售價 "+d(t.productData.price1?t.productData.price1.toLocaleString("zh-TW",g):0)+" 元 ",1),a("p",K,[e[3]||(e[3]=x(" 社員價格 ")),a("span",X,d(t.productData.price2?t.productData.price2.toLocaleString("zh-TW",g):0),1),e[4]||(e[4]=x(" 元 "))])])):p(k)?(s(),o("p",Z,[h(f,{color:"red",class:"text-xs md:text-sm"},{default:D(()=>e[5]||(e[5]=[x("會員價")])),_:1,__:[5]}),x("NT"+d(t.productData.price2?t.productData.price2.toLocaleString("zh-TW",g):0)+"元 ",1)])):(s(),o("p",$," NT"+d(t.productData.price1?t.productData.price1.toLocaleString("zh-TW",g):0)+"元 ",1))]),a("button",{type:"button",onClick:C,class:"w-3/4 py-1 rounded-lg bg-black text-white duration-300 mt-2"}," 加入購物車 ")])])}}},et=L(_,[["__scopeId","data-v-f05a51a0"]]);export{et as P};
