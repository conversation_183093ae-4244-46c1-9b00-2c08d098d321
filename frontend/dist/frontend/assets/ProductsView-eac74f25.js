import{_ as J,q as Q,s as G,a as K,r as v,N as X,H as R,o as Y,O as Z,b as ee,P as te,c as A,Q as se,d as D,e as g,f as m,g as r,h as N,w as z,u as h,t as S,C as ae,R as oe,F as B,i as E,j as V,l as W,p as re}from"./index-da48dc81.js";import{P as ne}from"./ProductItem-96281f12.js";import{P as le}from"./PageItem-95afc634.js";const ie={class:"container m-auto flex flex-col mt-20 px-10 xl:p-0"},ce={class:"flex"},ue={class:"w-1/5 mt-16 hidden md:block"},de={class:"w-full md:w-4/5"},ge={class:"text-center text-4xl font-bold"},pe={class:"flex justify-between mb-4"},fe={key:0},me={class:"flex justify-center items-center mt-4 mb-6"},ve={class:"ml-2"},he={class:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"},ye={key:1,class:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"},we={key:2,class:"text-center py-10"},_e=5*60*1e3,Pe={__name:"ProductsView",setup(xe){const b=Q(),{classSet:u}=G(b),{postApiData:ke,getApiData:j,delApiData:be,cancelRequests:Ce}=K(),f=v(!1),w=v("載入中...");v(null);let _=v([]);const d=X();d.query;const i=R({currentPage:1,pageSize:20,total:0}),$=v(null),P=R({categories:null,lastCacheTime:null}),L=e=>e?Date.now()-e<_e:!1;Y(async()=>{try{f.value=!0,w.value="正在初始化...",await T(),(!u.value||u.value.length===0)&&(console.warn("⚠️ 分類數據載入失敗，嘗試重新載入"),await b.getClassSet()),await y(d.query),await x()}catch(e){console.error("ProductsView: 初始化失敗:",e),f.value=!1}});const T=async()=>{try{if(P.categories&&L(P.lastCacheTime))return;if(w.value="載入分類數據...",await b.getClassSet(),await new Promise(e=>setTimeout(e,100)),!u.value||u.value.length===0)throw console.warn("⚠️ 分類數據未正確載入，使用默認分類"),new Error("分類數據載入失敗");P.categories=u.value,P.lastCacheTime=Date.now()}catch(e){console.error("💥 載入分類失敗:",e)}},y=async(e,t=1,s=0)=>{try{w.value="載入商品數據...",s===0&&(f.value=!0);const o={...e,page:t,pageSize:i.pageSize};if(o.sort&&o.sort!==""&&o.sort!=="全部商品"){if(o.sort==="商品館"){const a=[1,3,5,11,12,16,18,19,20,21,22,38,4,13,14,15,17,23,25];o.categories=a.join(",")}else if((!u.value||u.value.length===0)&&(console.warn("⚠️ 分類數據尚未載入，嘗試重新載入分類"),await T()),u.value&&u.value.length>0){const a=q(u.value,o.sort);if(a){const l=H(a);o.categories=l.join(",")}else console.warn("⚠️ 找不到指定分類:",o.sort)}}const n=await j("product",o);if(n){const a=Array.isArray(n.resData)?n.resData:[],l=n.resTotal||n.total||0,p=await F(a);_.value=p,i.total=l,i.currentPage=t}else console.warn("⚠️ 無效的API響應"),_.value=[],i.total=0}catch(o){if(console.error("💥 載入商品失敗:",o),s<2)return console.log(`🔄 重試載入商品 (${s+1}/2)`),await new Promise(n=>setTimeout(n,1e3*(s+1))),y(e,t,s+1);_.value=[],i.total=0}finally{s===0&&(f.value=!1)}},x=async()=>{await Z();try{window.scrollTo(0,0),window.scrollTo({top:0,left:0,behavior:"smooth"}),document.documentElement.scrollTop=0,document.body.scrollTop=0}catch(e){console.error("滾動失敗:",e)}},O=async e=>{i.currentPage=e,await y(d.query,e),await x()},U=async e=>{i.pageSize=e,i.currentPage=1,await y(d.query,1),await x()},F=async(e,t=50)=>{const s=[];for(let c=0;c<e.length;c+=t){const o=e.slice(c,c+t);await Promise.resolve();const n=o.map(a=>{let l=[];try{if(a.images){if(Array.isArray(a.images))l=a.images;else if(typeof a.images=="string"){const p=a.images.trim();p.startsWith("[")&&p.endsWith("]")?l=JSON.parse(p):l=[p]}}}catch(p){console.warn("⚠️ 圖片解析錯誤:",a.id,p,"原始圖片數據:",a.images),typeof a.images=="string"&&a.images.trim()!==""?l=[a.images.trim()]:l=[]}return{...a,images:l}});s.push(...n)}return s},H=e=>{const t=[],s=e.id||parseInt(e.key);s!=null&&!isNaN(s)&&t.push(s);const c=n=>{!n||!n.length||n.forEach(a=>{const l=a.id||parseInt(a.key);l!=null&&!isNaN(l)&&t.push(l),a.children&&a.children.length>0&&c(a.children)})};return e.children&&e.children.length>0&&c(e.children),[...new Set(t)].sort((n,a)=>n-a)},q=(e,t)=>{for(const s of e)if(s.value===t||s.name===t||s.label===t)return s;for(const s of e)if(s.children){const c=q(s.children,t);if(c)return c}return null},C=ee.debounce(async e=>{i.currentPage=1,await y(e,1),await x()},300);te(()=>d.query,e=>{C(e)});const k=A(()=>_.value);se(()=>{C.cancel&&C.cancel()});const I=v("最新上架"),M=A(()=>{if(!k.value||k.value.length===0)return[];const e=[...k.value];switch(I.value){case"價格低到高":return e.sort((t,s)=>(t.price1||0)-(s.price1||0));case"價格高到低":return e.sort((t,s)=>(s.price1||0)-(t.price1||0));default:return e}});return(e,t)=>{const s=D("RouterLink"),c=D("a-tree");return g(),m("div",ie,[r("div",ce,[r("div",ue,[N(c,{class:"text-xl","tree-data":h(u).filter(o=>o.label!=="品牌館"),defaultExpandAll:!0},{title:z(({value:o,label:n,key:a})=>[N(s,{to:{path:"/products",query:{sort:o}},style:{"line-height":"2"}},{default:z(()=>[re(S(n),1)]),_:2},1032,["to"])]),_:1},8,["tree-data"])]),r("div",de,[r("p",ge,S(h(d).query.sort==""?"全部商品":h(d).query.keyWord?"搜尋:"+h(d).query.keyWord:h(d).query.sort),1),r("div",pe,[t[2]||(t[2]=r("div",null,null,-1)),ae(r("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>I.value=o)},t[1]||(t[1]=[r("option",null,"最新上架",-1),r("option",null,"價格低到高",-1),r("option",null,"價格高到低",-1)]),512),[[oe,I.value]])]),f.value?(g(),m("div",fe,[r("div",me,[t[3]||(t[3]=r("div",{class:"spinner"},null,-1)),r("span",ve,S(w.value),1)]),r("div",he,[(g(!0),m(B,null,E(i.pageSize,o=>(g(),m("div",{key:o,class:"animate-pulse"},t[4]||(t[4]=[r("div",{class:"aspect-square bg-gray-200 rounded-lg mb-4"},null,-1),r("div",{class:"h-4 bg-gray-200 rounded mb-2"},null,-1),r("div",{class:"h-4 bg-gray-200 rounded w-3/4 mb-2"},null,-1),r("div",{class:"h-6 bg-gray-200 rounded w-1/2"},null,-1)])))),128))])])):(g(),m("div",ye,[(g(!0),m(B,null,E(M.value,(o,n)=>(g(),W(ne,{key:`${o.id}-${n}`,productData:o,keyWord:h(d).query.sort},null,8,["productData","keyWord"]))),128))])),!f.value&&k.value.length===0?(g(),m("div",we,t[5]||(t[5]=[r("i",{class:"fas fa-search text-4xl text-gray-400 mb-4"},null,-1),r("p",{class:"text-gray-500"},"未找到符合條件的商品",-1)]))):V("",!0),i.total>0?(g(),W(le,{key:3,ref_key:"pageRef",ref:$,totalPage:i.total,initialPage:i.currentPage,pageSize:i.pageSize,loading:f.value,onPageChange:O,onPageSizeChange:U,class:"mt-8"},null,8,["totalPage","initialPage","pageSize","loading"])):V("",!0)])])])}}},qe=J(Pe,[["__scopeId","data-v-a90f8963"]]);export{qe as default};
