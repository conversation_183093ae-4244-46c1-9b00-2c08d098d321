import{_ as q,G as F,q as M,s as P,r as T,H as C,c as X,P as z,o as E,b as O,x as A,y as G,v as H,d as J,e as u,f as p,u as w,g as t,h,t as I,B as K,p as x,C as c,E as m,z as U,j as $,w as V,F as Q,L as W}from"./index-da48dc81.js";const B=""+new URL("task-df33db08.svg",import.meta.url).href;const Y={key:0,class:"container m-auto px-6 xl:px-0 py-10"},Z={class:"grid grid-cols-1 xl:grid-cols-3 gap-10"},ee={class:"bg-gray-200 p-8 rounded-lg md:col-span-2"},te={class:"flex justify-end mt-4 border-t-2 border-gray-400 pt-4"},se={class:"text-2xl font-bold"},oe={class:"xl:mt-20"},le={class:"mb-4"},re={key:0,class:"text-red-500 text-sm mt-1"},ne={class:"mb-4"},ae={key:0,class:"text-red-500 text-sm mt-1"},ie={key:1,class:"text-gray-500 text-sm mt-1"},de={class:"mb-4"},ue={class:"mb-4"},pe={class:"mb-4"},ve={class:"mb-4"},ce={class:"mb-4 col-span-2"},me={class:"col-span-2"},ge={key:0,class:"text-red-500 text-center mb-2"},fe=["disabled"],be={key:1,class:"container m-auto py-10 text-center flex flex-col items-center",style:{"min-height":"50vh"}},xe={class:"mt-10"},_e={key:2,class:"container m-auto text-center py-20"},ye={class:"flex flex-col justify-center items-center"},he={class:"mt-10"},ke={__name:"CheckView",setup(we){const _=F(),D=M(),{clientData:k,carts:a,total:y,isUser:R}=P(D),v=T(!1),o=C({receiver:"",phone:"",detailed_address:"",cooperative:"",invoice_title:"",tax_id:"",notes:""}),r=C({receiver:"",phone:"",detailed_address:"",cooperative:"",invoice_title:"",tax_id:"",notes:""}),n=C({receiver:!1,phone:!1,detailed_address:!1,cooperative:!1,invoice_title:!1,tax_id:!1,notes:!1}),i=T(""),N=l=>{if(!/^[0-9]+$/.test(l))return"電話號碼只能包含數字";const d=/^09\d{8}$/,s=/^0\d{1,2}\d{6,8}$/;return!d.test(l)&&!s.test(l)?"請輸入有效的台灣電話號碼格式":""},g=()=>{o.receiver.trim()?r.receiver="":r.receiver="請輸入訂購人姓名",o.phone.trim()?r.phone=N(o.phone):r.phone="請輸入聯絡電話"},f=l=>{n[l]=!0,g()},j=l=>{const e=l.target.value;o.phone=e.replace(/\D/g,""),n.phone=!0,g()},S=X(()=>o.receiver.trim()!==""&&o.phone.trim()!==""&&r.receiver===""&&r.phone==="");z(a,l=>{(!l||l.length===0)&&!v.value&&(console.log("購物車已清空，導向首頁"),setTimeout(()=>{!v.value&&(!a.value||a.value.length===0)&&_.push("/")},100))},{deep:!0}),E(()=>{const l=localStorage.getItem("token"),e=localStorage.getItem("isLogin");if(!e||!l){localStorage.setItem("redirect_after_login",window.location.href),_.push("/login");return}if(e&&l&&!R.value&&(R.value=!0,console.log("已同步設置用戶登入狀態")),(!a.value||a.value.length===0)&&!v.value){console.log("購物車為空，導向首頁"),_.push("/");return}k.value&&(o.receiver=k.value.name||"",o.phone=k.value.phone||"",o.receiver&&(n.receiver=!0),o.phone&&(n.phone=!0),g())});const L=O.throttle(async()=>{var l,e,d;if(i.value="",n.receiver=!0,n.phone=!0,g(),!S.value){i.value="請填寫所有必填欄位並修正錯誤";return}if(!a.value||a.value.length===0){i.value="購物車是空的，請先添加商品",_.push("/");return}if(!y.value||y.value<=0){i.value="訂單總金額有誤，請重新整理頁面";return}try{const s={receiver:o.receiver,phone:o.phone,address:"自取/無須配送",detailed_address:o.detailed_address,cooperative:o.cooperative,invoice_title:o.invoice_title,tax_id:o.tax_id,notes:o.notes,products:[...a.value],total:y.value,client:((l=k.value)==null?void 0:l.id)||"1",status:1};console.log("送出訂單資料:",s);const b=await A.post(G("orderList"),s,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`}});console.log("訂單提交結果:",b.data),b.data&&(b.data.success||b.status==200||b.status==201)?(v.value=!0,a.value=[],y.value=0,localStorage.setItem("carts",JSON.stringify([])),H.success("訂單提交成功！"),setTimeout(()=>{v.value&&_.push("/account")},3e3)):i.value=((e=b.data)==null?void 0:e.message)||"訂單提交失敗，請稍後再試"}catch(s){console.error("訂單提交錯誤:",s),s.response?i.value=`訂單提交失敗：${s.response.status} - ${((d=s.response.data)==null?void 0:d.message)||"伺服器錯誤"}`:s.request?i.value="無法連接到伺服器，請檢查網路連接並稍後再試":i.value=`訂單提交過程中發生錯誤：${s.message}`}},2e3,{trailing:!1});return(l,e)=>{const d=J("RouterLink");return u(),p(Q,null,[w(a).length>0?(u(),p("div",Y,[t("div",Z,[t("div",ee,[e[16]||(e[16]=t("h3",{class:"text-3xl font-bold mb-6"},"購物明細",-1)),h(W),t("div",te,[t("p",se,"訂單總金額: NT$ "+I(w(y).toLocaleString()),1)])]),t("div",oe,[e[24]||(e[24]=t("h4",{class:"text-3xl font-bold"},"訂購資料",-1)),e[25]||(e[25]=t("p",{class:"text-gray-600 mb-4"},null,-1)),t("form",{onSubmit:e[15]||(e[15]=K((...s)=>w(L)&&w(L)(...s),["prevent"])),class:"grid grid-cols-2 gap-4 mt-4"},[t("div",le,[e[17]||(e[17]=t("label",{for:"receiver",class:"block mb-1"},[x(" 訂購人 "),t("span",{class:"text-red-500"},"*")],-1)),c(t("input",{id:"receiver",class:U(["w-full border-2 p-2 rounded",{"border-red-500":n.receiver&&r.receiver}]),placeholder:"請輸入姓名","onUpdate:modelValue":e[0]||(e[0]=s=>o.receiver=s),onInput:e[1]||(e[1]=s=>f("receiver")),onBlur:e[2]||(e[2]=s=>{n.receiver=!0,g()}),required:""},null,34),[[m,o.receiver]]),n.receiver&&r.receiver?(u(),p("p",re,I(r.receiver),1)):$("",!0)]),t("div",ne,[e[18]||(e[18]=t("label",{for:"phone",class:"block mb-1"},[x(" 聯絡電話 "),t("span",{class:"text-red-500"},"*")],-1)),c(t("input",{id:"phone",class:U(["w-full border-2 p-2 rounded",{"border-red-500":n.phone&&r.phone}]),placeholder:"請輸入連絡電話 (僅數字)","onUpdate:modelValue":e[3]||(e[3]=s=>o.phone=s),onInput:j,onBlur:e[4]||(e[4]=s=>{n.phone=!0,g()}),required:""},null,34),[[m,o.phone]]),n.phone&&r.phone?(u(),p("p",ae,I(r.phone),1)):(u(),p("p",ie," 請輸入有效的台灣手機或市話號碼 "))]),t("div",de,[e[19]||(e[19]=t("label",{for:"detailed_address",class:"block mb-1"}," 住址（詳細地址） ",-1)),c(t("input",{id:"detailed_address",class:"w-full border-2 p-2 rounded",placeholder:"可選填","onUpdate:modelValue":e[5]||(e[5]=s=>o.detailed_address=s),onInput:e[6]||(e[6]=s=>f("detailed_address"))},null,544),[[m,o.detailed_address]])]),t("div",ue,[e[20]||(e[20]=t("label",{for:"cooperative",class:"block mb-1"}," 儲互社（XX區XX社） ",-1)),c(t("input",{id:"cooperative",class:"w-full border-2 p-2 rounded",placeholder:"可選填","onUpdate:modelValue":e[7]||(e[7]=s=>o.cooperative=s),onInput:e[8]||(e[8]=s=>f("cooperative"))},null,544),[[m,o.cooperative]])]),t("div",pe,[e[21]||(e[21]=t("label",{for:"invoice_title",class:"block mb-1"}," 公司抬頭 ",-1)),c(t("input",{id:"invoice_title",class:"w-full border-2 p-2 rounded",placeholder:"可選填","onUpdate:modelValue":e[9]||(e[9]=s=>o.invoice_title=s),onInput:e[10]||(e[10]=s=>f("invoice_title"))},null,544),[[m,o.invoice_title]])]),t("div",ve,[e[22]||(e[22]=t("label",{for:"tax_id",class:"block mb-1"}," 統編 ",-1)),c(t("input",{id:"tax_id",class:"w-full border-2 p-2 rounded",placeholder:"可選填","onUpdate:modelValue":e[11]||(e[11]=s=>o.tax_id=s),onInput:e[12]||(e[12]=s=>f("tax_id"))},null,544),[[m,o.tax_id]])]),t("div",ce,[e[23]||(e[23]=t("label",{for:"notes",class:"block mb-1"}," 備註 ",-1)),c(t("textarea",{id:"notes",class:"w-full border-2 p-2 rounded",placeholder:"可選填",rows:"3","onUpdate:modelValue":e[13]||(e[13]=s=>o.notes=s),onInput:e[14]||(e[14]=s=>f("notes"))},null,544),[[m,o.notes]])]),t("div",me,[i.value?(u(),p("p",ge,I(i.value),1)):$("",!0),t("button",{type:"submit",class:U(["bg-black text-white w-full p-3 rounded-lg hover:bg-gray-800 transition mt-4",{"opacity-50 cursor-not-allowed":!S.value}]),disabled:!S.value}," 送出訂單 ",10,fe)])],32)])])])):$("",!0),v.value?(u(),p("div",be,[e[28]||(e[28]=t("img",{src:B,class:"w-20",alt:"menu"},null,-1)),e[29]||(e[29]=t("p",{class:"text-2xl mt-4 mb-6"},"成功送出訂單",-1)),e[30]||(e[30]=t("p",{class:"text-gray-600 mb-4"},"您的訂單已成功提交，請到店自取商品",-1)),t("div",xe,[h(d,{to:"/",class:"mr-4 bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-black"},{default:V(()=>e[26]||(e[26]=[x(" 返回首頁 ")])),_:1,__:[26]}),h(d,{to:"/account",class:"bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"},{default:V(()=>e[27]||(e[27]=[x(" 查看訂單 ")])),_:1,__:[27]})])])):v.value?(u(),p("div",_e,[t("div",ye,[e[33]||(e[33]=t("img",{src:B,class:"w-20",alt:"menu"},null,-1)),e[34]||(e[34]=t("p",{class:"text-2xl mt-4 mb-6"},"成功送出訂單",-1)),e[35]||(e[35]=t("p",{class:"text-gray-600 mb-4"},"您的訂單已成功提交，請到店自取商品",-1)),t("div",he,[h(d,{to:"/",class:"mr-4 bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-black"},{default:V(()=>e[31]||(e[31]=[x(" 返回首頁 ")])),_:1,__:[31]}),h(d,{to:"/account",class:"bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"},{default:V(()=>e[32]||(e[32]=[x(" 查看訂單 ")])),_:1,__:[32]})])])])):$("",!0)],64)}}},$e=q(ke,[["__scopeId","data-v-5affdd39"]]);export{$e as default};
