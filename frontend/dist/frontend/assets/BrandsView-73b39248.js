import{q as G,s as H,a as J,r as y,N as K,G as Q,H as W,o as X,O as S,b as Y,P as Z,c as q,d as A,e as u,f as p,g as s,h as D,w as j,C as ee,R as te,F as z,i as N,j as R,l as B,p as oe,t as ae}from"./index-da48dc81.js";import{P as se}from"./ProductItem-96281f12.js";import{P as re}from"./PageItem-95afc634.js";const ne={class:"container m-auto flex flex-col mt-20 px-10 xl:p-0"},le={class:"flex"},ce={class:"w-1/5 mt-16 hidden md:block"},ie={class:"w-full md:w-4/5"},de={class:"flex justify-between mb-4"},ue={key:0},ge={class:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"},fe={key:1,class:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"},pe={key:2,class:"text-center py-10 text-gray-500"},we={__name:"BrandsView",setup(me){const x=G(),{classSet:l}=H(x),{getApiData:k}=J(),i=y([]),C=y([]),m=K(),E=Q();m.query;const r=W({currentPage:1,pageSize:20,total:0}),F=y(null),g=y(!1);X(async()=>{try{g.value=!0,await Promise.all([w(),$()]);const t={...m.query};t.sort||(t.sort="品牌館"),Object.keys(m.query).length===0&&await E.replace({path:"/brands",query:t}),await v(t,1)}catch(t){console.error("💥 品牌館初始化失敗:",t)}finally{g.value=!1,await S(),window.scrollTo({top:0,behavior:"smooth"})}});const $=async()=>{var t;try{const e=await k("brands");((t=e==null?void 0:e.resData)==null?void 0:t.length)>0&&(C.value=e.resData)}catch(e){console.error("載入品牌數據失敗:",e)}},w=async()=>{try{await x.getClassSet()}catch(t){console.error("載入分類失敗:",t)}},v=async(t,e=1)=>{try{e===1&&(g.value=!0);const o={...t,page:e,pageSize:r.pageSize},d=performance.now();if(console.log("🚀 開始API請求:",o),o.sort&&o.sort!==""&&o.sort!=="全部商品"){if(o.sort==="品牌館"){const n=[2,6,24,26,27,30,31,7,36,37,32,33,34,35];o.categories=n.join(",")}else if(o.sort==="鼎王生活館"){const n=[6,7,35];o.categories=n.join(",")}else if(o.sort==="源順食品"){const n=[24,36,37];o.categories=n.join(",")}else if(o.sort==="那汝娃國際有限公司"){const n=[31,32,33,34];o.categories=n.join(",")}else if(o.sort==="屈臣氏watsons"||o.sort==="ASUS 華碩電腦"||o.sort==="電動代步車"){if((!l.value||l.value.length===0)&&(console.warn("⚠️ 分類數據尚未載入，嘗試重新載入分類"),await w()),l.value&&l.value.length>0){const n=P(l.value,o.sort);if(n){const h=I(n);o.categories=h.join(",")}else console.warn("⚠️ 找不到指定分類:",o.sort)}}else if((!l.value||l.value.length===0)&&(console.warn("⚠️ 分類數據尚未載入，嘗試重新載入分類"),await w()),l.value&&l.value.length>0){const n=P(l.value,o.sort);if(n){const h=I(n);o.categories=h.join(",")}else console.warn("⚠️ 找不到指定分類:",o.sort)}}o.brand&&delete o.brand;const a=await k("product",o),f=performance.now()-d;if(console.log(`⏱️  API響應時間: ${f.toFixed(2)}ms`),f>1e3?console.warn("⚠️ API響應時間過長，可能是後端性能問題"):f>500?console.log("⚠️ API響應時間偏長，建議優化"):console.log("✅ API響應時間正常"),a&&a.resData)if(Array.isArray(a.resData)){const n=performance.now();i.value=a.resData,r.total=a.resTotal||a.total||i.value.length,r.currentPage=e;const U=performance.now()-n;console.log(`📊 數據處理時間: ${U.toFixed(2)}ms`),console.log(`📦 載入商品數量: ${i.value.length}, 總數: ${r.total}`)}else console.warn("⚠️ API返回的resData不是數組:",a.resData),i.value=[],r.total=0;else console.warn("⚠️ 無法獲取商品資料或資料為空:",a),i.value=[],r.total=0}catch(o){console.error("💥 獲取商品資料時發生錯誤:",o),i.value=[],r.total=0}finally{g.value=!1}},I=t=>{const e=[],o=t.id||parseInt(t.key);o!=null&&!isNaN(o)&&e.push(o);const d=a=>{!a||!a.length||a.forEach(c=>{const f=c.id||parseInt(c.key);f!=null&&!isNaN(f)&&e.push(f),c.children&&c.children.length>0&&d(c.children)})};return t.children&&t.children.length>0&&d(t.children),e},P=(t,e)=>{for(const o of t)if(o.value===e||o.name===e||o.label===e)return o;for(const o of t)if(o.children){const d=P(o.children,e);if(d)return d}return console.warn("⚠️ 未找到匹配的分類:",e),null},b=async()=>{await S();try{window.scrollTo(0,0),window.scrollTo({top:0,left:0,behavior:"smooth"}),document.documentElement.scrollTop=0,document.body.scrollTop=0}catch(t){console.error("滾動失敗:",t)}},L=async t=>{console.log("分頁變更:",t),r.currentPage=t;const e={...m.query};e.sort||(e.sort="品牌館"),await v(e,t),await b()},V=async t=>{console.log("分頁大小變更:",t),r.pageSize=t,r.currentPage=1;const e={...m.query};e.sort||(e.sort="品牌館"),await v(e,1),await b()},M=Y.debounce(async t=>{r.currentPage=1;const e={...t};e.sort||(e.sort="品牌館"),await v(e,1),await b()},300);Z(()=>m.query,t=>{g.value&&r.total===0||M(t)},{deep:!0});const _=y("最新上架"),T=q(()=>{if(!i.value||!Array.isArray(i.value))return[];const t=[...i.value];switch(_.value){case"價格低到高":return t.sort((e,o)=>(e.price1||0)-(o.price1||0));case"價格高到低":return t.sort((e,o)=>(o.price1||0)-(e.price1||0));default:return t}}),O=q(()=>{const t=l.value.find(e=>e.label==="品牌館");return t&&t.children&&t.children.length>0?[t]:[{label:"品牌館",value:"brands",selectable:!1,children:C.value.map(e=>({label:e.name,value:e.name,key:e.id}))}]});return(t,e)=>{const o=A("RouterLink"),d=A("a-tree");return u(),p("div",ne,[s("div",le,[s("div",ce,[D(d,{class:"text-xl","tree-data":O.value,defaultExpandAll:!0},{title:j(({value:a,label:c})=>[D(o,{to:{path:"/brands",query:{sort:a}},class:"brand-link"},{default:j(()=>[oe(ae(c),1)]),_:2},1032,["to"])]),_:1},8,["tree-data"])]),s("div",ie,[e[6]||(e[6]=s("p",{class:"text-center text-4xl font-bold"},null,-1)),s("div",de,[e[2]||(e[2]=s("div",null,null,-1)),ee(s("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>_.value=a)},e[1]||(e[1]=[s("option",null,"最新上架",-1),s("option",null,"價格低到高",-1),s("option",null,"價格高到低",-1)]),512),[[te,_.value]])]),g.value?(u(),p("div",ue,[e[4]||(e[4]=s("div",{class:"flex justify-center items-center py-6"},[s("div",{class:"spinner border-4 border-blue-200 border-t-blue-600 rounded-full w-8 h-8 animate-spin mr-3"}),s("span",null,"載入中...")],-1)),s("div",ge,[(u(!0),p(z,null,N(r.pageSize,a=>(u(),p("div",{key:a,class:"animate-pulse"},e[3]||(e[3]=[s("div",{class:"aspect-square bg-gray-200 rounded-lg mb-4"},null,-1),s("div",{class:"h-4 bg-gray-200 rounded mb-2"},null,-1),s("div",{class:"h-4 bg-gray-200 rounded w-3/4 mb-2"},null,-1),s("div",{class:"h-6 bg-gray-200 rounded w-1/2"},null,-1)])))),128))])])):(u(),p("div",fe,[(u(!0),p(z,null,N(T.value,(a,c)=>(u(),B(se,{key:c,productData:a},null,8,["productData"]))),128))])),!g.value&&T.value.length===0?(u(),p("div",pe,e[5]||(e[5]=[s("i",{class:"fas fa-box-open text-4xl mb-4"},null,-1),s("p",null,"此分類暫無商品",-1)]))):R("",!0),r.total>0?(u(),B(re,{key:3,ref_key:"pageRef",ref:F,totalPage:r.total,initialPage:r.currentPage,pageSize:r.pageSize,loading:g.value,onPageChange:L,onPageSizeChange:V,class:"mt-8"},null,8,["totalPage","initialPage","pageSize","loading"])):R("",!0)])])])}}};export{we as default};
